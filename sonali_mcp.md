Sonali App Development 
Phase 1: Project Setup & Configuration
1: Initial Project Structure
- Frontend: React 19 with TypeScript, Vite build tool
- Backend: Node.js 20 LTS with TypeScript, Express.js
- Database: MySQL 8.x with prisma ORM
- Project structure:
sonali-app/ ├── frontend/ │ ├── src/ │ │ ├── components/ │ │ ├── pages/ │ │ ├── hooks/ │ │ ├── services/ │ │ ├── types/ │ │ ├── utils/ │ │ └── styles/ │ ├── public/ │ └── package.json ├── backend/ │ ├── src/ │ │ ├── controllers/ │ │ ├── models/ │ │ ├── routes/ │ │ ├── middleware/ │ │ ├── services/ │ │ ├── utils/ │ │ └── config/ │ └── package.json ├── shared/ │ └── types/ └── docker-compose.yml
- VPS (root@**************)
2: Database  & Models using prixma:
1. Branches table with fields: id, name, address, manager_id, is_active, timestamps
2. Users table with fields: id, name, email, password, is_active, role (enum: admin, manager, field_officer, member), member_id (unique), branch_id, last_login_at, timestamps
3. Members table with fields: id, member_id (unique), name, father_or_husband_name, mother_name, present_address, permanent_address, nid_number (unique), date_of_birth, religion, phone_number, blood_group, photo, occupation, reference_id, branch_id, created_by, is_active, timestamps
4. Loan Applications table with fields: id, member_id, applied_amount, reason, loan_cycle_number, recommender, advance_payment, status (enum: pending, approved, rejected), reviewed_by, reviewed_at, applied_at, timestamps
5. Loans table with fields: id, loan_application_id (unique), loan_date, loan_amount, total_repayment_amount, repayment_duration, repayment_method (enum: weekly, monthly), installment_count, installment_amount, advance_payment, first_installment_date, last_installment_date, timestamps
6. Installments table with fields: id, loan_id, installment_no, installment_date, installment_amount, advance_paid, due, collected_by, collection_date, status (enum: pending, paid, overdue), timestamps
7. Branch Transactions table with fields: id, branch_id, transaction_type, created_by, entry_type (enum: income, expense), serial_no, date, description, account_no, category, voucher_no, amount, entered_by, timestamps
8. Saving Accounts table with fields: id, member_id, saving_type (enum: general, dps, fdr), joint_photo, nominee_name, nominee_relation, saving_method (enum: daily, monthly), monthly_amount, fdr_amount, start_date, created_by, is_active, timestamps
9. Advertisements table with fields: id, title, image, link_url, is_active, display_order, start_date, end_date, timestamps

3: Authentication & Security Setup
- JWT-based authentication with refresh tokens
- Password hashing using bcrypt
- Login using Member ID and Password (not email)
- Role-based access control middleware
- Remember me functionality
- Session management
- Rate limiting for login attempts
- Password reset functionality
- Code obfuscation for frontend (webpack configuration to prevent inspect element viewing)
- CORS configuration for domain: www.sonalibd.org
- Input validation and sanitization
- SQL injection prevention
- XSS protection

Phase 2: Backend API Development
4: Authentication APIs
- POST /api/auth/login - Login with member_id and password, role-based response
- POST /api/auth/logout - Logout and invalidate tokens
- POST /api/auth/refresh - Refresh access token
- POST /api/auth/forgot-password - Send password reset email
- POST /api/auth/reset-password - Reset password with token
- GET /api/auth/profile - Get current user profile
- Implement proper error handling and validation
- Add rate limiting (5 attempts per minute)
- Include login activity logging

5: User Management APIs :
- GET /api/users - Get all users with pagination and filtering
- POST /api/users - Create new user
- GET /api/users/:id - Get user by ID
- PUT /api/users/:id - Update user
- DELETE /api/users/:id - Delete user
- POST /api/users/bulk-import - Bulk import users
- GET /api/users/export - Export users data
- PUT /api/users/:id/reset-password - Reset user password
- PUT /api/users/:id/toggle-status - Activate/deactivate user
- Include proper validation and authorization middleware

6: Branch Management APIs
- GET /api/branches - Get all branches (admin), get own branch (manager/field officer)
- POST /api/branches - Create new branch (admin only)
- GET /api/branches/:id - Get branch details
- PUT /api/branches/:id - Update branch (admin only)
- DELETE /api/branches/:id - Delete branch (admin only)
- GET /api/branches/:id/users - Get branch users
- GET /api/branches/:id/members - Get branch members
- GET /api/branches/:id/performance - Get branch performance metrics
- Include proper authorization based on user role

7: Member Management APIs
- GET /api/members - Get members (with role-based filtering)
- POST /api/members - Create new member
- GET /api/members/:id - Get member details
- PUT /api/members/:id - Update member
- DELETE /api/members/:id - Delete member
- GET /api/members/search - Search members with autocomplete
- POST /api/members/upload-photo - Upload member photo
- GET /api/members/export - Export members data (admin only)
- GET /api/members/:id/loans - Get member loan history
- GET /api/members/:id/savings - Get member savings
- Include file upload handling for photos

8: Loan Management APIs
- GET /api/loan-applications - Get loan applications (role-based filtering)
- POST /api/loan-applications - Create new loan application
- GET /api/loan-applications/:id - Get loan application details
- PUT /api/loan-applications/:id - Update loan application
- PUT /api/loan-applications/:id/approve - Approve loan application (manager only)
- PUT /api/loan-applications/:id/reject - Reject loan application (manager only)
- GET /api/loans - Get active loans
- GET /api/loans/:id - Get loan details
- GET /api/loans/:id/installments - Get loan installment schedule
- POST /api/loans/calculator - Loan calculation endpoint
- Include loan calculation logic: Total Repayment - Advance Payment = Installment Amounts

9: Installment Collection APIs
- GET /api/installments - Get installments (role-based filtering)
- GET /api/installments/pending - Get pending installments
- GET /api/installments/overdue - Get overdue installments
- POST /api/installments/:id/collect - Collect installment payment
- GET /api/installments/:id/history - Get installment payment history
- GET /api/installments/member/:memberId - Get member installments
- PUT /api/installments/:id/status - Update installment status
- Include automatic overdue calculation and status updates

10: Financial Management APIs
- GET /api/transactions - Get branch transactions (role-based)
- POST /api/transactions - Create new transaction (manager only)
- GET /api/transactions/:id - Get transaction details
- PUT /api/transactions/:id - Update transaction
- DELETE /api/transactions/:id - Delete transaction
- GET /api/transactions/summary - Get financial summary
- GET /api/analytics/branch/:branchId - Get branch analytics (admin/manager)
- GET /api/analytics/system - Get system-wide analytics (admin only)
- GET /api/reports/generate - Generate custom reports
- Include monthly/yearly financial summaries

3: Frontend Development
11: UI Components Library
Create a modern, responsive UI components library using React 19 with TypeScript:
- Button component with variants (primary, secondary, danger, outline)
- Input components (text, password, number, date, textarea, select)
- Modal/Dialog components
- Table component with sorting, pagination, and search
- Card components for dashboards
- Form components with validation
- Loading spinners and skeletons
- Alert/notification components
- Sidebar and navigation components
- Mobile-responsive design with Tailwind CSS
- Dark/light theme support
- Accessibility compliance (ARIA labels, keyboard navigation)

12: Authentication Pages
- Login page with Member ID and Password fields
- Form validation with proper error messages
- Remember me checkbox functionality
- Forgot password modal
- Advertisement display box (managed by admin)
- Role-based redirection after login (Admin, Manager, Field Officer, Member)
- Mobile-responsive design
- Loading states and error handling
- Password strength indicator
- Login attempt tracking

13: Dashboard Components:

1. Field Officer Dashboard:
   - Personal performance metrics display
   - Total members under supervision counter
   - Total collections made display
   - Recent activities timeline
   - Quick action buttons: Member Registration, Loan Application, Installment Collection, Loan Calculator

2. Branch Manager Dashboard:
   - Branch performance overview
   - Number of field officers in branch
   - Total active loans counter
   - Installment collection status (pending, overdue, collected)
   - Quick action buttons: Loan approval dashboard, Branch Income/Expenditure Management, Member Registration, Loan Application, Installment Collection, Loan Calculator

3. Admin Dashboard:
   - System-wide overview widgets
   - Total branches, users, field officers, members counters
   - Overall financial performance charts
   - Recent activities feed
   - Quick action buttons: User Management, Branch Management, Member Management, Finance Management, Advertisement Management, Analysis and Reporting, Loan Calculator

4. Member Dashboard (view only):
   - Personal financial overview
   - Current savings balance display
   - Active loan status and details
   - Recent installment payment history
   - Next payment due date and amount
   - Mobile-optimized clean interface

14: Member Management Components:
- Member Registration Form with all fields from members table
- Photo upload functionality with preview and crop
- Address fields (present and permanent)
- Reference selection dropdown with search
- Form validation and error handling
- Success/failure notifications
- Member search and listing component
- Member details view component
- Member edit component
- Bulk operations interface
- Export functionality
- Print member cards feature

15: Loan Management Components:
- Loan Application Form with member selection using modern search
- Applied amount input with validation
- Loan reason textarea
- Loan cycle number selector
- Recommender information fields
- Advance payment input
- Real-time loan calculator integration
- Loan Approval Dashboard for managers
- Pending applications list
- Detailed application review interface
- Approve/Reject functionality with comments
- Application history and audit trail
- Loan details view component

16: Installment Collection Components:
- Member selection with modern search and autocomplete
- Loan details display with installment schedule
- Installment collection form with amount validation
- Payment history display
- Collection status updates
- Overdue installments highlighting
- Bulk collection interface
- Receipt generation and printing
- Collection reports and summaries
- Mobile-optimized collection interface

17: Financial Management Components:
- Branch Income and Expenditure Dashboard
- Transaction entry form with all required fields
- Transaction listing with search and filter
- Monthly/yearly financial summaries
- Category management
- Voucher number tracking
- Financial reports generation
- Charts and graphs for financial data
- Export functionality (PDF)
- Print reports feature

18: Administration Components:
- User Management System with CRUD operations
- Role assignment interface
- Permission management
- Bulk user import/export
- Branch Management interface
- Advertisement Management system
- Upload/Remove advertisement images
- Advertisement preview
- System analytics dashboard
- Custom report generation
- Data visualization charts
- System settings panel



Phase 5: Security & Performance
22: Code Obfuscation & Security
Implement frontend code obfuscation and security measures:
- Webpack configuration for code obfuscation
- Disable right-click context menu
- Disable developer tools (F12)
- Disable text selection on sensitive areas
- Implement CSP (Content Security Policy)
- Add integrity checks for critical files
- Environment-specific builds
- Secure API endpoints
- Input sanitization
- XSS prevention measures

23: Performance Optimization
Implement performance optimization:
- Code splitting and lazy loading
- Image optimization and lazy loading
- Bundle size optimization
- Caching strategies
- Memoization for expensive operations
- Virtual scrolling for large lists
- Database query optimization
- API response caching
- Compression for assets
- CDN setup for static files



Important Notes:
1.	Code Obfuscation: Use webpack-obfuscator to prevent code inspection
2.	Security: Implement proper authentication and authorization
3.	Performance: Optimize for mobile devices and slow networks
4.	Responsive Design: Ensure all components work on mobile devices
5.	Error Handling: Implement comprehensive error handling
6.	Validation: Validate all inputs on both client and server
7.	Testing: Write tests for critical functionality
8.	Documentation: Document all APIs and components
9.	Deployment: Use automated deployment scripts
10.	Monitoring: Implement proper logging and monitoring
Development Order:
Execute these prompts in sequence to avoid duplication and ensure proper dependency management. Each prompt builds upon the previous ones, creating a complete, secure, and performant application.

