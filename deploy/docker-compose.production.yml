version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: sonali-mysql-prod
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: sonali_app_production
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    networks:
      - sonali-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Backend API
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile.production
    container_name: sonali-backend-prod
    restart: always
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: sonali_app_production
      DB_USER: ${MYSQL_USER}
      DB_PASSWORD: ${MYSQL_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES_IN: 7d
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      JWT_REFRESH_EXPIRES_IN: 30d
      BCRYPT_SALT_ROUNDS: 12
      CORS_ORIGIN: ${FRONTEND_URL}
    ports:
      - "3001:3001"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - sonali-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (Built and served by Nginx)
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile.production
      args:
        VITE_API_URL: ${API_URL}
        VITE_APP_NAME: "Sonali App"
        VITE_APP_VERSION: "1.0.0"
        VITE_NODE_ENV: production
    container_name: sonali-frontend-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
    networks:
      - sonali-network

volumes:
  mysql_data:
    driver: local

networks:
  sonali-network:
    driver: bridge
