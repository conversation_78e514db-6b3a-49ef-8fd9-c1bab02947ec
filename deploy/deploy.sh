#!/bin/bash

# Sonali App Deployment Script for VPS
# Usage: ./deploy.sh [environment]
# Environment: development, staging, production (default: production)

set -e

# Configuration
VPS_HOST="**************"
VPS_USER="root"
APP_NAME="sonali-app"
DEPLOY_PATH="/opt/$APP_NAME"
ENVIRONMENT="${1:-production}"

echo "🚀 Starting deployment to $VPS_HOST for $ENVIRONMENT environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment file exists
if [ ! -f "deploy/.env.$ENVIRONMENT" ]; then
    print_error "Environment file deploy/.env.$ENVIRONMENT not found!"
    exit 1
fi

# Create deployment package
print_status "Creating deployment package..."
tar -czf sonali-app-deploy.tar.gz \
    --exclude='node_modules' \
    --exclude='.git' \
    --exclude='dist' \
    --exclude='*.log' \
    --exclude='.env' \
    .

# Upload to VPS
print_status "Uploading to VPS..."
scp sonali-app-deploy.tar.gz $VPS_USER@$VPS_HOST:/tmp/

# Deploy on VPS
print_status "Deploying on VPS..."
ssh $VPS_USER@$VPS_HOST << EOF
    set -e
    
    # Create deployment directory
    mkdir -p $DEPLOY_PATH
    cd $DEPLOY_PATH
    
    # Backup current deployment if exists
    if [ -d "current" ]; then
        echo "Backing up current deployment..."
        mv current backup-\$(date +%Y%m%d-%H%M%S) || true
    fi
    
    # Extract new deployment
    echo "Extracting new deployment..."
    mkdir -p current
    cd current
    tar -xzf /tmp/sonali-app-deploy.tar.gz
    
    # Copy environment file
    cp deploy/.env.$ENVIRONMENT .env
    
    # Install Docker and Docker Compose if not installed
    if ! command -v docker &> /dev/null; then
        echo "Installing Docker..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
        systemctl enable docker
        systemctl start docker
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo "Installing Docker Compose..."
        curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
    fi
    
    # Stop existing containers
    echo "Stopping existing containers..."
    docker-compose -f deploy/docker-compose.production.yml down || true
    
    # Build and start new containers
    echo "Building and starting containers..."
    docker-compose -f deploy/docker-compose.production.yml up -d --build
    
    # Wait for services to be healthy
    echo "Waiting for services to be healthy..."
    sleep 30
    
    # Check if services are running
    if docker-compose -f deploy/docker-compose.production.yml ps | grep -q "Up"; then
        echo "✅ Deployment successful!"
        echo "🌐 Application should be available at: http://$VPS_HOST"
        echo "📊 API health check: http://$VPS_HOST:3001/health"
    else
        echo "❌ Deployment failed! Check container logs:"
        docker-compose -f deploy/docker-compose.production.yml logs
        exit 1
    fi
    
    # Cleanup
    rm -f /tmp/sonali-app-deploy.tar.gz
    
    # Remove old backups (keep last 5)
    cd $DEPLOY_PATH
    ls -t backup-* 2>/dev/null | tail -n +6 | xargs rm -rf || true
EOF

# Cleanup local files
rm -f sonali-app-deploy.tar.gz

print_status "Deployment completed successfully!"
print_status "Application URL: http://$VPS_HOST"
print_status "API Health Check: http://$VPS_HOST:3001/health"

# Optional: Run health check
print_status "Running health check..."
sleep 10
if curl -f "http://$VPS_HOST:3001/health" > /dev/null 2>&1; then
    print_status "✅ Health check passed!"
else
    print_warning "⚠️  Health check failed. Please check the application manually."
fi

echo ""
echo "🎉 Deployment complete!"
echo "📝 Next steps:"
echo "   1. Configure your domain DNS to point to $VPS_HOST"
echo "   2. Set up SSL certificates for HTTPS"
echo "   3. Configure firewall rules if needed"
echo "   4. Set up monitoring and backups"
