{"name": "sonali-app", "version": "1.0.0", "description": "A modern full-stack web application built with React 19, TypeScript, Node.js, Express, and MySQL", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && npm run preview", "start:backend": "cd backend && npm start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf frontend/node_modules backend/node_modules frontend/dist backend/dist", "docker:dev": "docker-compose up -d", "docker:prod": "docker-compose -f deploy/docker-compose.production.yml up -d", "docker:down": "docker-compose down", "deploy": "cd deploy && ./deploy.sh", "deploy:staging": "cd deploy && ./deploy.sh staging", "deploy:production": "cd deploy && ./deploy.sh production"}, "keywords": ["react", "typescript", "nodejs", "express", "mysql", "sequelize", "fullstack", "vite", "tailwindcss"], "author": "Sonali App Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "workspaces": ["frontend", "backend", "shared/types"], "dependencies": {"cors": "^2.8.5", "express": "^5.1.0"}}