{"name": "sonali-backend", "version": "1.0.0", "description": "Backend for Sonali App - Node.js with TypeScript and Express", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "dev:watch": "concurrently \"tsc -w\" \"nodemon dist/index.js\"", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "clean": "<PERSON><PERSON><PERSON> dist", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "keywords": ["nodejs", "typescript", "express", "mysql", "sequelize"], "author": "Sonali App Team", "license": "MIT", "dependencies": {"@prisma/client": "^6.11.1", "@types/multer": "^2.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "isomorphic-dompurify": "^2.26.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "prisma": "^6.11.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.13", "concurrently": "^9.2.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}